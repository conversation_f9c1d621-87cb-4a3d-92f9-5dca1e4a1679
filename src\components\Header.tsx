import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import Logo from './Logo';
import { Menu, X, Palette, Building, Info, Sun, Moon, ChevronDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Link, useLocation } from 'react-router-dom';
const Header = () => {
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(true); // Default to dark mode
  const [currentLanguage, setCurrentLanguage] = useState('EN');
  const [languageDropdownOpen, setLanguageDropdownOpen] = useState(false);

  // Available languages
  const languages = [
    { code: 'EN', name: 'English' },
    { code: 'NL', name: 'Nederlands' },
    { code: 'FR', name: 'Français' },
  ];

  // Get active page from current location
  const getActivePage = () => {
    switch (location.pathname) {
      case '/':
        return 'artists';
      case '/agency':
        return 'agency';
      case '/about':
        return 'about';
      case '/contact':
        return 'contact';
      default:
        return 'artists';
    }
  };

  const activePage = getActivePage();

  useEffect(() => {
    // Apply the theme to the document when it changes
    if (isDarkMode) {
      document.documentElement.classList.remove('light-mode');
      document.documentElement.classList.add('dark-mode');
    } else {
      document.documentElement.classList.remove('dark-mode');
      document.documentElement.classList.add('light-mode');
    }
  }, [isDarkMode]);
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };
  
  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const handleLanguageChange = (languageCode: string) => {
    setCurrentLanguage(languageCode);
    setLanguageDropdownOpen(false);
  };

  const getCurrentLanguage = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0];
  };
  return <div className="sticky top-0 z-50 pt-8 px-4">
      <header className="w-full max-w-7xl mx-auto py-3 px-6 md:px-8 flex items-center justify-between">
        <div className="p-3">
          <Logo />
        </div>
        
        {/* Mobile menu button */}
        <button className="md:hidden p-3 rounded-2xl text-muted-foreground hover:text-foreground" onClick={toggleMobileMenu}>
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
        
        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center absolute left-1/2 transform -translate-x-1/2">
          <div className="rounded-full px-1 py-1 backdrop-blur-md bg-background/80 border border-border shadow-lg">
            <ToggleGroup type="single" value={activePage}>
              <ToggleGroupItem value="artists" className={cn("px-4 py-2 rounded-full transition-colors relative", activePage === 'artists' ? 'text-accent-foreground bg-accent' : 'text-muted-foreground hover:text-foreground hover:bg-muted')} asChild>
                <Link to="/">
                  <Palette size={16} className="inline-block mr-1.5" /> Artists
                </Link>
              </ToggleGroupItem>
              <ToggleGroupItem value="agency" className={cn("px-4 py-2 rounded-full transition-colors relative", activePage === 'agency' ? 'text-accent-foreground bg-accent' : 'text-muted-foreground hover:text-foreground hover:bg-muted')} asChild>
                <Link to="/agency">
                  <Building size={16} className="inline-block mr-1.5" /> Agency
                </Link>
              </ToggleGroupItem>
              <ToggleGroupItem value="about" className={cn("px-4 py-2 rounded-full transition-colors relative", activePage === 'about' ? 'text-accent-foreground bg-accent' : 'text-muted-foreground hover:text-foreground hover:bg-muted')} asChild>
                <Link to="/about">
                  <Info size={16} className="inline-block mr-1.5" /> About
                </Link>
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </nav>
        
        {/* Mobile navigation */}
        {mobileMenuOpen && <div className="md:hidden absolute top-20 left-4 right-4 bg-background/95 backdrop-blur-md py-4 px-6 border border-border rounded-2xl shadow-lg z-50">
            <div className="flex flex-col gap-4">
              <Link 
                to="/" 
                className={`px-3 py-2 text-sm rounded-md transition-colors ${activePage === 'artists' ? 'bg-accent text-accent-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-muted'}`} 
                onClick={() => setMobileMenuOpen(false)}
              >
                <Palette size={16} className="inline-block mr-1.5" /> Artists
              </Link>
              <Link 
                to="/agency" 
                className={`px-3 py-2 text-sm rounded-md transition-colors ${activePage === 'agency' ? 'bg-accent text-accent-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-muted'}`} 
                onClick={() => setMobileMenuOpen(false)}
              >
                <Building size={16} className="inline-block mr-1.5" /> Agency
              </Link>
              <Link 
                to="/about" 
                className={`px-3 py-2 text-sm rounded-md transition-colors ${activePage === 'about' ? 'bg-accent text-accent-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-muted'}`} 
                onClick={() => setMobileMenuOpen(false)}
              >
                <Info size={16} className="inline-block mr-1.5" /> About
              </Link>
              
              {/* Theme and Language toggles for mobile */}
              <div className="flex items-center justify-between px-3 py-2">
                <span className="text-sm text-muted-foreground">Theme</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleTheme}
                  className="h-8 w-8 p-0"
                >
                  {isDarkMode ? <Sun size={16} /> : <Moon size={16} />}
                </Button>
              </div>
              <div className="px-3 py-2">
                <span className="text-sm text-muted-foreground mb-2 block">Language</span>
                <div className="grid grid-cols-3 gap-2">
                  {languages.map((language) => (
                    <button
                      key={language.code}
                      onClick={() => handleLanguageChange(language.code)}
                      className={cn(
                        "flex items-center justify-center px-3 py-2 rounded-xl transition-all duration-200",
                        "hover:bg-muted/50 focus:bg-muted/50 focus:outline-none",
                        currentLanguage === language.code
                          ? "bg-accent/20 text-accent-foreground border border-accent/30"
                          : "text-muted-foreground hover:text-foreground"
                      )}
                    >
                      <span className="text-sm font-medium">{language.code}</span>
                      {currentLanguage === language.code && (
                        <Check size={12} className="ml-1 text-accent" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>}
        
        <div className="hidden md:flex items-center gap-2">
          {/* Theme toggle for desktop */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="h-9 w-9 p-0 text-muted-foreground hover:text-foreground hover:bg-muted rounded-2xl"
          >
            {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}
          </Button>
          
          {/* Language dropdown for desktop */}
          <DropdownMenu open={languageDropdownOpen} onOpenChange={setLanguageDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 px-3 text-muted-foreground hover:text-foreground hover:bg-muted rounded-2xl text-sm font-medium transition-all duration-200"
              >
                {getCurrentLanguage().code}
                <ChevronDown
                  size={14}
                  className={cn(
                    "ml-1 transition-transform duration-200",
                    languageDropdownOpen && "rotate-180"
                  )}
                />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[80px] rounded-2xl backdrop-blur-md bg-background/95 border border-border shadow-xl animate-in fade-in-0 zoom-in-95 duration-200"
            >
              {languages.map((language) => (
                <DropdownMenuItem
                  key={language.code}
                  onClick={() => handleLanguageChange(language.code)}
                  className={cn(
                    "flex items-center justify-center py-2.5 rounded-xl transition-all duration-200 cursor-pointer",
                    "hover:bg-muted/50 focus:bg-muted/50",
                    currentLanguage === language.code
                          ? "bg-accent/20 text-accent-foreground border border-accent/30"
                          : "text-muted-foreground hover:text-foreground"
                    currentLanguage === language.code && "bg-accent text-accent-foreground hover:bg-accent/90"
                  )}
                >
                  <span className="text-sm font-medium">{language.code}</span>
                  
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Contact button */}
          <Button variant="ghost" className="text-muted-foreground hover:text-foreground hover:bg-muted rounded-2xl h-9 px-4" asChild>
            <Link to="/contact">Contact us</Link>
          </Button>
        </div>
      </header>
    </div>;
};
export default Header;